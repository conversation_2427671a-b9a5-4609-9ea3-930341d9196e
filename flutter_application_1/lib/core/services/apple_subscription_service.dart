import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import '../utils/storage_utils.dart';
import '../config/sandbox_config.dart';

/// Apple订阅服务
/// 处理Apple内购订阅相关功能
class AppleSubscriptionService {
  static const String _monthlySubscriptionId = 'LemiVip001';
  static const String _quarterlySubscriptionId = 'LimeVip_quarter';
  static const String _yearlySubscriptionId = 'LimeVip_yearly';
  static const String _oneYearPackageId = 'LimeVip_AYear'; // 新增非续期订阅

  // InAppPurchase实例
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;

  // 产品信息缓存
  List<ProductDetails> _products = [];
  bool _isInitialized = false;
  bool _isInitializing = false;

  // 订阅状态缓存
  bool? _cachedPremiumStatus;
  DateTime? _lastCheckTime;
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  // 测试用的订阅状态（仅在调试模式下使用）
  bool? _testSubscriptionStatus;

  // 购买状态回调
  Function(bool success, String? error)? _purchaseCallback;

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    if (_isInitializing) return; // 防止重复初始化

    _isInitializing = true;

    try {
      SandboxConfig.log('开始初始化Apple订阅服务');
      SandboxConfig.log('当前环境: ${SandboxConfig.environmentDescription}');

      // 只在非iOS平台跳过Apple服务初始化
      if (!Platform.isIOS) {
        SandboxConfig.log('非iOS平台：跳过Apple服务初始化');
        _isInitialized = true;
        _isInitializing = false;
        return;
      }

      SandboxConfig.log('开始初始化Apple订阅服务...');
      SandboxConfig.log('沙盒模式: ${SandboxConfig.isSandboxMode}');

      // 检查平台支持
      debugPrint('检查StoreKit可用性...');
      final bool available = await _inAppPurchase.isAvailable().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          debugPrint('StoreKit可用性检查超时');
          return false;
        },
      );

      if (!available) {
        debugPrint('❌ App Store不可用 - 可能原因：');
        debugPrint('  • 在模拟器上运行');
        debugPrint('  • 网络连接问题');
        debugPrint('  • Apple服务器不可用');
        debugPrint('  • 设备限制设置');
        _isInitialized = true; // 标记为已初始化，避免重复尝试
        _isInitializing = false;
        return;
      }

      debugPrint('✅ StoreKit可用，继续初始化...');

      // 监听购买更新
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => _subscription.cancel(),
        onError: (error) => debugPrint('购买流错误: $error'),
      );

      // 加载产品信息
      await _loadProducts();

      // 检查现有购买
      await restorePurchases();

      _isInitialized = true;
      debugPrint('Apple订阅服务初始化完成');
    } catch (e) {
      debugPrint('初始化Apple订阅服务失败: $e');
      _isInitialized = true; // 即使失败也标记为已初始化，避免重复尝试
    } finally {
      _isInitializing = false; // 确保重置初始化标志
    }
  }

  /// 加载产品信息
  Future<void> _loadProducts() async {
    try {
      final Set<String> productIds = {
        _monthlySubscriptionId,
        _quarterlySubscriptionId,
        _yearlySubscriptionId,
        _oneYearPackageId,
      };

      debugPrint('开始查询产品: ${productIds.join(", ")}');

      final ProductDetailsResponse response = await _inAppPurchase
          .queryProductDetails(productIds)
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () {
              debugPrint('产品查询超时');
              return ProductDetailsResponse(
                productDetails: [],
                notFoundIDs: productIds.toList(),
                error: null,
              );
            },
          );

      if (response.error != null) {
        debugPrint('❌ 加载产品失败: ${response.error}');
        debugPrint('  错误代码: ${response.error!.code}');
        debugPrint('  错误来源: ${response.error!.source}');
        debugPrint('  错误消息: ${response.error!.message}');
        debugPrint('  错误详情: ${response.error!.details}');
        return;
      }

      _products = response.productDetails;
      debugPrint('✅ 成功加载了 ${_products.length} 个产品');

      if (_products.isNotEmpty) {
        for (final product in _products) {
          debugPrint('  • ${product.id}: ${product.title} - ${product.price}');
        }
      }

      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('⚠️ 未找到的产品ID: ${response.notFoundIDs.join(", ")}');
        debugPrint('  可能原因:');
        debugPrint('  • App Store Connect中产品未配置');
        debugPrint('  • 产品状态不是"准备提交"');
        debugPrint('  • Bundle ID不匹配');
        debugPrint('  • 产品ID拼写错误');
      }
    } catch (e) {
      debugPrint('❌ 加载产品信息异常: $e');
    }
  }

  /// 处理购买更新
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      debugPrint('购买状态更新: ${purchaseDetails.status}');

      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          debugPrint('购买等待中...');
          break;
        case PurchaseStatus.purchased:
        case PurchaseStatus.restored:
          _verifyAndUnlock(purchaseDetails);
          break;
        case PurchaseStatus.error:
          debugPrint('购买错误: ${purchaseDetails.error}');
          _purchaseCallback?.call(false, purchaseDetails.error?.message);
          break;
        case PurchaseStatus.canceled:
          debugPrint('用户取消购买');
          _purchaseCallback?.call(false, '用户取消购买');
          break;
      }

      // 完成购买
      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// 验证并解锁功能
  Future<void> _verifyAndUnlock(PurchaseDetails purchaseDetails) async {
    try {
      debugPrint('验证购买: ${purchaseDetails.productID}');

      // 保存订阅信息到本地
      await _saveSubscriptionStatus(purchaseDetails);

      // 更新缓存
      _cachedPremiumStatus = true;
      _lastCheckTime = DateTime.now();

      _purchaseCallback?.call(true, null);
      debugPrint('订阅解锁成功');
    } catch (e) {
      debugPrint('验证购买失败: $e');
      _purchaseCallback?.call(false, '验证失败');
    }
  }

  /// 保存订阅状态
  Future<void> _saveSubscriptionStatus(PurchaseDetails purchaseDetails) async {
    final now = DateTime.now();
    final expiryDate = _calculateExpiryDate(purchaseDetails.productID, now);

    final subscriptionData = {
      'productId': purchaseDetails.productID,
      'purchaseDate': now.toIso8601String(),
      'transactionDate': purchaseDetails.transactionDate,
      'purchaseID': purchaseDetails.purchaseID,
      'expiryDate': expiryDate.toIso8601String(),
      'isActive': true,
      'platform': 'apple',
    };

    await StorageUtils.saveSubscriptionData(subscriptionData);
  }

  /// 计算订阅到期时间
  DateTime _calculateExpiryDate(String productId, DateTime purchaseDate) {
    switch (productId) {
      case _monthlySubscriptionId: // LemiVip001
        return purchaseDate.add(const Duration(days: 30));
      case _quarterlySubscriptionId: // LimeVip_quarter
        return purchaseDate.add(const Duration(days: 90));
      case _yearlySubscriptionId: // LimeVip_yearly
        return purchaseDate.add(const Duration(days: 365));
      case _oneYearPackageId: // LimeVip_AYear
        return purchaseDate.add(const Duration(days: 365));
      default:
        // 默认为月度订阅
        return purchaseDate.add(const Duration(days: 30));
    }
  }

  /// 检查用户是否为付费用户
  Future<bool> isPremiumUser() async {
    try {
      // 如果有有效缓存，直接返回
      if (_cachedPremiumStatus != null &&
          _lastCheckTime != null &&
          DateTime.now().difference(_lastCheckTime!) < _cacheValidDuration) {
        debugPrint('使用缓存的订阅状态: $_cachedPremiumStatus');
        return _cachedPremiumStatus!;
      }

      // 在非iOS平台返回false
      if (!Platform.isIOS) {
        debugPrint('非iOS平台，返回false');
        _cachedPremiumStatus = false;
        _lastCheckTime = DateTime.now();
        return false;
      }

      // 在iOS模拟器上，优先检查测试订阅状态
      if (kDebugMode) {
        debugPrint('调试模式：检查测试订阅状态');
        if (_testSubscriptionStatus != null) {
          debugPrint('使用测试订阅状态: $_testSubscriptionStatus');
          _cachedPremiumStatus = _testSubscriptionStatus;
          _lastCheckTime = DateTime.now();
          return _testSubscriptionStatus!;
        }

        final testStatus = await _checkLocalSubscriptionStatus();
        _cachedPremiumStatus = testStatus;
        _lastCheckTime = DateTime.now();
        debugPrint('本地测试订阅状态: $testStatus');
        return testStatus;
      }

      // 确保已初始化
      if (!_isInitialized) {
        debugPrint('Apple订阅服务未初始化，开始初始化');
        await initialize();
      }

      // 检查Apple订阅状态
      final appleStatus = await _checkAppleSubscriptionStatus();
      if (appleStatus) {
        _cachedPremiumStatus = true;
        _lastCheckTime = DateTime.now();
        debugPrint('Apple订阅状态: true');
        return true;
      }

      // 检查本地存储的订阅状态
      final localStatus = await _checkLocalSubscriptionStatus();

      // 更新缓存
      _cachedPremiumStatus = localStatus;
      _lastCheckTime = DateTime.now();
      debugPrint('最终订阅状态: $localStatus');

      return localStatus;
    } catch (e) {
      debugPrint('检查付费状态失败: $e');

      // 出错时返回本地缓存或false
      final fallbackStatus = _cachedPremiumStatus ?? false;
      debugPrint('使用fallback状态: $fallbackStatus');
      return fallbackStatus;
    }
  }

  /// 检查Apple订阅状态
  Future<bool> _checkAppleSubscriptionStatus() async {
    try {
      if (!_isInitialized) return false;

      // 查询过去的购买记录
      await _inAppPurchase.restorePurchases();

      // 这里应该检查购买流中的状态更新
      // 由于是异步的，我们依赖本地存储的状态
      return await _checkLocalSubscriptionStatus();
    } catch (e) {
      debugPrint('检查Apple订阅状态失败: $e');
      return false;
    }
  }

  /// 检查本地订阅状态
  Future<bool> _checkLocalSubscriptionStatus() async {
    try {
      // 从本地存储检查订阅状态
      final subscriptionData = await StorageUtils.getSubscriptionData();
      if (subscriptionData != null) {
        final expiryDate = DateTime.tryParse(subscriptionData['expiryDate'] ?? '');
        if (expiryDate != null && expiryDate.isAfter(DateTime.now())) {
          return true;
        }
      }

      // TODO: 集成in_app_purchase插件后，这里检查Apple的订阅状态
      // 目前返回测试状态
      if (kDebugMode) {
        // 在调试模式下，可以通过本地设置模拟付费状态
        final testPremium = await StorageUtils.getTestPremiumStatus();
        return testPremium ?? false;
      }

      return false;
    } catch (e) {
      debugPrint('检查本地订阅状态失败: $e');
      return false;
    }
  }

  /// 购买订阅
  Future<bool> purchaseSubscription(String productId, {Function(bool success, String? error)? callback}) async {
    try {
      if (!Platform.isIOS) {
        throw PlatformException(
          code: 'PLATFORM_NOT_SUPPORTED',
          message: 'Apple订阅仅在iOS平台支持',
        );
      }

      // 确保已初始化
      if (!_isInitialized) {
        await initialize();
      }

      // 设置购买回调
      _purchaseCallback = callback;

      // 查找产品
      final ProductDetails? product = _products
          .where((p) => p.id == productId)
          .firstOrNull;

      if (product == null) {
        debugPrint('产品不存在: $productId');
        callback?.call(false, '产品不存在');
        return false;
      }

      // 发起购买
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: product,
      );

      debugPrint('开始购买: ${product.title}');

      // 根据产品类型选择购买方法
      final bool success;
      if (productId == _oneYearPackageId) {
        // 非续期订阅使用 buyNonConsumable
        success = await _inAppPurchase.buyNonConsumable(
          purchaseParam: purchaseParam,
        );
      } else {
        // 自动续期订阅使用 buyConsumable (in_app_purchase 3.x API)
        success = await _inAppPurchase.buyConsumable(
          purchaseParam: purchaseParam,
        );
      }

      if (!success) {
        callback?.call(false, '购买请求失败');
      }

      return success;
    } catch (e) {
      debugPrint('购买订阅失败: $e');
      callback?.call(false, e.toString());
      return false;
    }
  }

  /// 恢复购买
  Future<bool> restorePurchases() async {
    try {
      if (!Platform.isIOS) {
        return false;
      }

      // 确保已初始化
      if (!_isInitialized) {
        await initialize();
      }

      debugPrint('开始恢复购买...');
      await _inAppPurchase.restorePurchases();

      // 恢复购买是异步的，通过购买流处理
      // 这里返回true表示恢复请求已发送
      return true;
    } catch (e) {
      debugPrint('恢复购买失败: $e');
      return false;
    }
  }

  /// 获取订阅产品信息
  Future<List<Map<String, dynamic>>> getSubscriptionProducts() async {
    try {
      debugPrint('获取订阅产品信息');

      // 在调试模式下，如果不是iOS平台，直接返回模拟数据
      if (kDebugMode && !Platform.isIOS) {
        debugPrint('调试模式且非iOS平台，返回模拟数据');
        return _getMockProducts();
      }

      // 确保已初始化
      if (!_isInitialized) {
        debugPrint('服务未初始化，开始初始化');
        try {
          await initialize().timeout(const Duration(seconds: 5));
        } catch (e) {
          debugPrint('初始化超时或失败: $e，返回模拟数据');
          return _getMockProducts();
        }
      }

      // 如果有真实产品信息，使用真实数据
      if (_products.isNotEmpty) {
        debugPrint('使用真实产品数据，产品数量: ${_products.length}');
        return _products.map((product) => {
          'id': product.id,
          'title': product.title,
          'description': product.description,
          'price': product.price,
          'rawPrice': product.rawPrice,
          'currencyCode': product.currencyCode,
        }).toList();
      }

      // 否则返回模拟数据
      debugPrint('使用模拟产品数据');
      return _getMockProducts();
    } catch (e) {
      debugPrint('获取订阅产品失败: $e');
      return _getMockProducts();
    }
  }


  /// 获取模拟产品数据
  List<Map<String, dynamic>> _getMockProducts() {
    return [
        {
          'id': _monthlySubscriptionId,
          'title': 'LimeFocus 高级版（月度）',
          'description': '解锁所有高级功能，包括云同步、高级统计等',
          'price': '¥6.00',
          'rawPrice': 6.0,
          'currencyCode': 'CNY',
        },
        {
          'id': _quarterlySubscriptionId,
          'title': 'LimeFocus 高级版（季度）',
          'description': '解锁所有高级功能，季度订阅享受优惠价格',
          'price': '¥12.00',
          'rawPrice': 12.0,
          'currencyCode': 'CNY',
        },
        {
          'id': _yearlySubscriptionId,
          'title': 'LimeFocus 高级版（年度）',
          'description': '解锁所有高级功能，年度订阅享受最大优惠',
          'price': '¥28.00',
          'rawPrice': 28.0,
          'currencyCode': 'CNY',
        },
        {
          'id': _oneYearPackageId,
          'title': 'LimeFocus 一年备考包',
          'description': '非续期订阅，一次性购买享受一年高级功能',
          'price': '¥18.00',
          'rawPrice': 18.0,
          'currencyCode': 'CNY',
        },
      ];
  }

  /// 获取订阅状态详情
  Future<Map<String, dynamic>?> getSubscriptionDetails() async {
    try {
      final subscriptionData = await StorageUtils.getSubscriptionData();
      if (subscriptionData != null) {
        final expiryDate = DateTime.tryParse(subscriptionData['expiryDate'] ?? '');
        final isActive = expiryDate != null && expiryDate.isAfter(DateTime.now());

        return {
          ...subscriptionData,
          'isActive': isActive,
          'daysRemaining': isActive
              ? expiryDate.difference(DateTime.now()).inDays
              : 0,
        };
      }

      // 在调试模式下，如果用户是高级用户但没有订阅数据，提供模拟数据
      if (kDebugMode && await isPremiumUser()) {
        final now = DateTime.now();
        return {
          'productId': _monthlySubscriptionId,
          'purchaseDate': now.subtract(const Duration(days: 15)).toIso8601String(),
          'expiryDate': now.add(const Duration(days: 15)).toIso8601String(),
          'purchaseID': 'test_purchase_${now.millisecondsSinceEpoch}',
          'isActive': true,
          'platform': 'apple',
          'daysRemaining': 15,
        };
      }

      return null;
    } catch (e) {
      debugPrint('获取订阅详情失败: $e');
      return null;
    }
  }

  /// 清除缓存（用于测试）
  void clearCache() {
    _cachedPremiumStatus = null;
    _lastCheckTime = null;
  }

  /// 清除沙盒测试数据
  Future<void> clearTestSubscriptionData() async {
    try {
      debugPrint('开始清除沙盒测试数据...');

      // 清除本地存储的订阅数据
      await StorageUtils.clearSubscriptionData();

      // 清除缓存
      _cachedPremiumStatus = null;
      _lastCheckTime = null;
      _testSubscriptionStatus = null;

      debugPrint('✅ 沙盒测试数据已清除');
    } catch (e) {
      debugPrint('❌ 清除沙盒测试数据失败: $e');
    }
  }

  /// 强制刷新订阅状态
  Future<bool> refreshSubscriptionStatus() async {
    try {
      debugPrint('强制刷新订阅状态...');

      // 清除缓存
      _cachedPremiumStatus = null;
      _lastCheckTime = null;

      // 重新检查订阅状态
      final status = await isPremiumUser();
      debugPrint('刷新后的订阅状态: $status');

      return status;
    } catch (e) {
      debugPrint('刷新订阅状态失败: $e');
      return false;
    }
  }





  /// 设置测试付费状态（仅用于开发）
  Future<void> setTestPremiumStatus(bool isPremium) async {
    if (kDebugMode) {
      await StorageUtils.setTestPremiumStatus(isPremium);
      _cachedPremiumStatus = isPremium;
      _lastCheckTime = DateTime.now();
    }
  }

  /// 释放资源
  void dispose() {
    try {
      _subscription.cancel();
      _isInitialized = false;
      debugPrint('Apple订阅服务已释放');
    } catch (e) {
      debugPrint('释放Apple订阅服务失败: $e');
    }
  }

  /// 获取产品详情
  ProductDetails? getProductDetails(String productId) {
    return _products.where((p) => p.id == productId).firstOrNull;
  }

  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;

  /// 获取可用产品数量
  int get availableProductsCount => _products.length;

  /// 设置测试订阅状态（仅用于开发调试）
  void setTestSubscriptionStatus(bool isSubscribed) {
    if (kDebugMode) {
      _testSubscriptionStatus = isSubscribed;
      _cachedPremiumStatus = isSubscribed;
      _lastCheckTime = DateTime.now();
      debugPrint('设置测试订阅状态: $isSubscribed');
    }
  }

  /// 清除测试订阅状态
  void clearTestSubscriptionStatus() {
    if (kDebugMode) {
      _testSubscriptionStatus = null;
      _cachedPremiumStatus = null;
      _lastCheckTime = null;
      debugPrint('清除测试订阅状态');
    }
  }
}

/// 订阅相关的异常
class SubscriptionException implements Exception {
  final String message;
  final String? code;

  const SubscriptionException(this.message, {this.code});

  @override
  String toString() => 'SubscriptionException: $message';
}
