import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../../shared/theme/constants.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/models/project_progress_change.dart';
import '../../../../core/services/enhanced_hive_service.dart';
import '../../utils/focus_data_calculator.dart';
import '../charts/mini_donut_chart.dart';
import '../../models/subject_distribution.dart';
import '../cards/today_project_progress_card.dart';

// 圆环图绘制器
class PieChartPainter extends CustomPainter {
  final List<SubjectDistribution> data;
  final List<Color> colors;

  PieChartPainter(this.data, this.colors);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 3, size.height / 2);
    final radius = size.width / 4;

    double startAngle = -90 * (math.pi / 180); // 从上方开始（-90度）

    for (int i = 0; i < data.length; i++) {
      final sweepAngle = data[i].percentage * 2 * math.pi;

      final paint = Paint()
        ..style = PaintingStyle.fill
        ..color = colors[i % colors.length];

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }

    // 绘制中心的白色圆形（形成环形）
    final centerPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.white;

    canvas.drawCircle(center, radius * 0.6, centerPaint);
  }

  @override
  bool shouldRepaint(PieChartPainter oldDelegate) => true;
}

/// 概览标签页
/// 显示专注数据的概览信息
class OverviewTab extends StatefulWidget {
  final List<FocusRecord> records;
  final String timeRange;

  const OverviewTab({
    super.key,
    required this.records,
    required this.timeRange,
  });

  @override
  State<OverviewTab> createState() => _OverviewTabState();
}

class _OverviewTabState extends State<OverviewTab> with AutomaticKeepAliveClientMixin {
  // Hive服务
  final EnhancedHiveService _hiveService = EnhancedHiveService();

  @override
  bool get wantKeepAlive => true;

  @override
  void didUpdateWidget(OverviewTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当传入的records发生变化时，触发重建
    if (widget.records != oldWidget.records) {
      debugPrint('OverviewTab: 检测到records变化，从 ${oldWidget.records.length} 条变为 ${widget.records.length} 条');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            // 触发重建，使用新的数据
          });
        }
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当页面重新获得焦点时刷新数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          // 触发重建，刷新数据
        });
      }
    });
  }

  // 构建指标项
  Widget _buildMetricItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 图标
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withAlpha(26), // 10% opacity (255 * 0.1 = 25.5)
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),

        // 数值
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.text,
          ),
        ),
        const SizedBox(height: 4),

        // 标签
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  // 构建紧凑型指标项（用于平均值卡片）
  Widget _buildCompactMetricItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 图标
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: color.withAlpha(26), // 10% opacity (255 * 0.1 = 25.5)
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),

        // 数值和标签
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColors.text,
              ),
            ),
            Text(
              label,
              style: const TextStyle(
                fontSize: 11,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 获取科目颜色
  Color _getSubjectColor(int index, {String? subjectId}) {
    // 如果提供了科目ID，尝试获取科目的实际颜色
    if (subjectId != null) {
      final subject = _hiveService.subjectRepository.getSubjectById(subjectId);
      if (subject != null) {
        return Color(subject.color);
      }
    }

    // 如果没有提供科目ID或找不到科目，使用默认颜色
    final colors = _getSubjectColors();
    return colors[index % colors.length];
  }

  // 获取科目颜色列表
  List<Color> _getSubjectColors() {
    return [
      AppColors.primary,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.cyan,
      Colors.amber,
      Colors.indigo,
      Colors.pink,
      Colors.grey,
    ];
  }







  // 获取今日科目分布数据
  List<SubjectDistribution> _getTodaySubjectDistribution() {
    debugPrint('_getTodaySubjectDistribution: 开始获取今日科目分布数据');
    final List<SubjectDistribution> data = [];

    try {
      // 确保仓库已初始化
      _hiveService.focusRecordRepository.init();
      _hiveService.subjectRepository.init();

      // 直接从仓库获取今日记录
      final todayRecords = _hiveService.focusRecordRepository.getTodayFocusRecords();
      debugPrint('_getTodaySubjectDistribution: 直接从仓库获取的今日记录数量: ${todayRecords.length}');

      // 如果有记录，打印第一条记录的详细信息
      if (todayRecords.isNotEmpty) {
        final firstRecord = todayRecords.first;
        debugPrint('_getTodaySubjectDistribution: 第一条记录详情: ID=${firstRecord.id}, 开始时间=${firstRecord.startTime}, 科目ID=${firstRecord.subjectId}, 时长=${firstRecord.durationSeconds / 60}分钟');
      }

      // 获取所有科目，用于显示空数据或填充数据
      final allSubjects = _hiveService.subjectRepository.getAllSubjects();
      debugPrint('_getTodaySubjectDistribution: 获取到 ${allSubjects.length} 个科目');

      // 如果没有科目，直接返回空列表
      if (allSubjects.isEmpty) {
        debugPrint('_getTodaySubjectDistribution: 没有科目，返回空列表');
        return data;
      }

      // 如果没有今日记录，显示空的科目数据
      if (todayRecords.isEmpty) {
        debugPrint('_getTodaySubjectDistribution: 今日没有专注记录，显示空的科目数据');

        // 为所有科目创建空的分布数据（最多显示5个）
        final subjectsToShow = allSubjects.length > 5 ? allSubjects.sublist(0, 5) : allSubjects;
        for (final subject in subjectsToShow) {
          data.add(SubjectDistribution(
            id: subject.id,
            name: subject.name,
            hours: 0,
            percentage: 0,
          ));
          debugPrint('_getTodaySubjectDistribution: 添加空科目 ${subject.name}');
        }

        return data;
      }

      // 按科目分组计算专注时长
      final Map<String, double> subjectHours = {};
      double totalHours = 0;

      // 计算每个科目的专注时长
      for (final record in todayRecords) {
        final subjectId = record.subjectId;
        final hours = record.durationSeconds / 3600.0;
        debugPrint('_getTodaySubjectDistribution: 记录 ID: ${record.id}, 科目 ID: $subjectId, 时长: ${hours.toStringAsFixed(2)} 小时');

        if (!subjectHours.containsKey(subjectId)) {
          subjectHours[subjectId] = 0;
        }

        subjectHours[subjectId] = (subjectHours[subjectId] ?? 0) + hours;
        totalHours += hours;
      }

      debugPrint('_getTodaySubjectDistribution: 总专注时长: ${totalHours.toStringAsFixed(2)} 小时');
      debugPrint('_getTodaySubjectDistribution: 科目数量: ${subjectHours.length}');

      // 为所有科目创建分布数据，包括没有今日记录的科目
      for (final subject in allSubjects) {
        final hours = subjectHours[subject.id] ?? 0.0;
        final percentage = totalHours > 0 ? (hours / totalHours).toDouble() : 0.0;

        data.add(SubjectDistribution(
          id: subject.id,
          name: subject.name,
          hours: hours,
          percentage: percentage,
        ));

        debugPrint('_getTodaySubjectDistribution: 科目 ${subject.name}, 时长: ${hours.toStringAsFixed(2)} 小时, 百分比: ${(percentage * 100).toStringAsFixed(1)}%');
      }

      // 按专注时长降序排序
      data.sort((a, b) => b.hours.compareTo(a.hours));
      debugPrint('_getTodaySubjectDistribution: 排序后的科目分布数据: ${data.map((e) => '${e.name}: ${e.hours.toStringAsFixed(2)}h').join(', ')}');
    } catch (e, stackTrace) {
      debugPrint('_getTodaySubjectDistribution: 获取今日科目分布数据出错: $e');
      debugPrint('_getTodaySubjectDistribution: 错误堆栈: $stackTrace');
    }

    return data;
  }

  // 获取昨日记录
  List<FocusRecord> _getYesterdayRecords() {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dayStart = DateTime(yesterday.year, yesterday.month, yesterday.day);
    final dayEnd = DateTime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59);

    return _hiveService.focusRecordRepository.getFocusRecordsByDateRange(dayStart, dayEnd);
  }

  // 获取进度变化记录
  List<ProjectProgressChange> _getProgressChanges() {
    try {
      // 获取所有进度变化记录（同步方法会自动检查初始化状态）
      final changes = _hiveService.projectProgressRepository.getAllProgressChanges();
      debugPrint('_getProgressChanges: 获取到 ${changes.length} 条进度变化记录');

      // 输出今日的进度变化记录
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);
      final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);

      final todayChanges = changes.where(
        (change) =>
            change.timestamp.isAfter(todayStart) &&
            change.timestamp.isBefore(todayEnd)
      ).toList();

      debugPrint('_getProgressChanges: 今日进度变化记录数量: ${todayChanges.length}');

      // 输出每条今日进度变化记录的详细信息
      for (final change in todayChanges) {
        debugPrint('_getProgressChanges: 项目ID: ${change.projectId}, 来源: ${change.source}, 值变化: ${change.valueChange}, 进度变化: ${change.progressChange}');
      }

      // 如果没有进度变化记录，创建一些模拟记录用于测试
      if (changes.isEmpty) {
        debugPrint('_getProgressChanges: 没有进度变化记录，创建模拟记录');

        // 获取所有项目
        final projects = _hiveService.subjectRepository.getAllProjects();
        final List<ProjectProgressChange> mockChanges = [];

        // 为每个开启了进度追踪的项目创建一个模拟记录
        for (final project in projects) {
          if (project.isTrackingEnabled && project.progress > 0) {
            final mockChange = ProjectProgressChange(
              id: 'mock_${DateTime.now().millisecondsSinceEpoch}_${project.id}',
              projectId: project.id,
              timestamp: DateTime.now(),
              previousValue: ((project.currentCustomValue ?? 0) - 1).toDouble(),
              newValue: (project.currentCustomValue ?? 0).toDouble(),
              previousProgress: project.progress - 0.01,
              newProgress: project.progress,
              source: ProgressChangeSource.manualAdjustment,
            );

            mockChanges.add(mockChange);
            debugPrint('_getProgressChanges: 创建模拟记录: ${mockChange.id}, 项目: ${project.name}, 进度变化: ${mockChange.progressChange}');
          }
        }

        return mockChanges;
      }

      return changes;
    } catch (e, stackTrace) {
      debugPrint('获取进度变化记录失败: $e');
      debugPrint('错误堆栈: $stackTrace');

      // 出错时返回一个空列表
      return [];
    }
  }

  // 构建科目分布项 - 竖向列表样式
  Widget _buildSubjectDistributionItem(SubjectDistribution subject, Color color) {
    debugPrint('_buildSubjectDistributionItem: 构建科目 ${subject.name}, 时长: ${subject.hours.toStringAsFixed(2)} 小时');

    // 如果时长为0，显示空状态
    final bool isEmpty = subject.hours <= 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 6), // 进一步减小底部间距
      child: Row(
        children: [
          // 左侧小圆点
          Container(
            width: 6, // 进一步减小为小圆点
            height: 6, // 进一步减小为小圆点
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isEmpty ? color.withAlpha(50) : color, // 使用纯色而非带边框的圆环
            ),
          ),
          const SizedBox(width: 6), // 进一步减小间距

          // 科目名称
          Expanded(
            child: Text(
              subject.name,
              style: TextStyle(
                fontSize: 12, // 进一步减小字体
                fontWeight: isEmpty ? FontWeight.normal : FontWeight.w500,
                color: isEmpty ? AppColors.textSecondary : AppColors.text,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 专注时长
          Text(
            isEmpty ? '0h' : '${subject.hours.toStringAsFixed(1)}h',
            style: TextStyle(
              fontSize: 11, // 进一步减小字体
              fontWeight: FontWeight.normal,
              color: isEmpty ? Colors.grey.shade400 : AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }



  // 获取今日记录
  List<FocusRecord> _getTodayRecords() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    // 优先使用传入的records参数，从中筛选今日记录
    final todayRecords = widget.records.where((record) {
      return record.startTime.isAfter(today) && record.startTime.isBefore(tomorrow);
    }).toList();

    debugPrint('_getTodayRecords: 从传入的 ${widget.records.length} 条记录中筛选出今日记录 ${todayRecords.length} 条');

    // 如果传入的records为空，则从Hive直接获取
    if (todayRecords.isEmpty && widget.records.isEmpty) {
      // 确保仓库已初始化
      _hiveService.focusRecordRepository.init();
      final hiveRecords = _hiveService.focusRecordRepository.getTodayFocusRecords();
      debugPrint('_getTodayRecords: 从Hive获取今日记录: ${hiveRecords.length} 条');
      return hiveRecords;
    }

    return todayRecords;
  }

  // 构建今日概览卡片
  Widget _buildTodayOverviewSection() {
    // 获取今日记录
    final todayRecords = _getTodayRecords();
    debugPrint('_buildTodayOverviewSection: 今日记录数量: ${todayRecords.length}');

    // 计算今日数据
    final todayHours = FocusDataCalculator.calculateTotalHours(todayRecords);
    final todaySessionCount = FocusDataCalculator.calculateSessionCount(todayRecords);
    final todayAvgMinutes = FocusDataCalculator.calculateAverageMinutes(todayRecords);

    // 获取今日科目分布
    final todaySubjects = _getTodaySubjectDistribution();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 今日概览标题
        const Text(
          '今日概览',
          style: AppTextStyles.headline3,
        ),
        const SizedBox(height: 12),

        // 今日数据卡片
        Container(
          padding: const EdgeInsets.all(AppSizes.paddingMedium),
          decoration: AppDecorations.standardCard(),
          child: Column(
            children: [
              // 今日数据指标行
              Row(
                children: [
                  // 今日专注时长
                  Expanded(
                    child: _buildMetricItem(
                      icon: Icons.access_time,
                      value: '${todayHours.toStringAsFixed(1)}h',
                      label: '今日专注',
                      color: AppColors.info,
                    ),
                  ),

                  // 今日专注次数
                  Expanded(
                    child: _buildMetricItem(
                      icon: Icons.repeat,
                      value: '$todaySessionCount次',
                      label: '专注次数',
                      color: AppColors.success,
                    ),
                  ),

                  // 今日平均时长
                  Expanded(
                    child: _buildMetricItem(
                      icon: Icons.timer,
                      value: '${todayAvgMinutes.toStringAsFixed(1)}分钟',
                      label: '平均时长',
                      color: AppColors.purple,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // 今日科目分布卡片
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(AppSizes.paddingMedium),
          decoration: AppDecorations.standardCard(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 科目列表或空状态提示
              if (todaySubjects.isNotEmpty)
                // 有数据状态 - 高度与上面卡片保持一致
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center, // 改为居中对齐
                  children: [
                    // 左侧圆环图 - 占比约1/3，靠左一些
                    Container(
                      width: 120, // 减小宽度
                      padding: const EdgeInsets.only(left: 0), // 减小左边距
                      child: MiniDonutChart(
                        data: todaySubjects,
                        colors: todaySubjects.map((s) => _getSubjectColor(0, subjectId: s.id)).toList(),
                        size: 80, // 调整圆环尺寸
                        strokeWidth: 10, // 调整圆环粗细
                      ),
                    ),
                    const SizedBox(width: 12), // 减小间距

                    // 右侧科目列表 - 占比约2/3
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 2), // 减小垂直内边距
                        child: Column(
                          mainAxisSize: MainAxisSize.min, // 使列高度适应内容
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 竖向列表，最多显示5个科目
                            for (int i = 0; i < todaySubjects.length && i < 5; i++)
                              _buildSubjectDistributionItem(
                                todaySubjects[i],
                                _getSubjectColor(i, subjectId: todaySubjects[i].id),
                              ),

                            // 如果有更多科目，显示查看更多按钮
                            if (todaySubjects.length > 5)
                              Align(
                                alignment: Alignment.centerRight,
                                child: TextButton(
                                  onPressed: () {
                                    // 切换到项目标签页
                                    final tabController = DefaultTabController.of(context);
                                    // 切换到"项目"标签页（假设是第二个标签页）
                                    tabController.animateTo(1);
                                  },
                                  style: TextButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    minimumSize: const Size(0, 0),
                                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Text(
                                        '查看更多',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: AppColors.primary,
                                        ),
                                      ),
                                      const SizedBox(width: 2),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        size: 10,
                                        color: AppColors.primary,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              else
                // 空状态提示 - 更简约美观
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center, // 居中对齐
                  children: [
                    // 左侧空圆环图 - 占比约1/3
                    Container(
                      width: 70, // 减小宽度
                      padding: const EdgeInsets.only(left: 0), // 减小左边距
                      child: MiniDonutChart(
                        data: const [],
                        colors: _getSubjectColors(),
                        size: 70, // 调整圆环尺寸
                        strokeWidth: 10, // 调整圆环粗细
                      ),
                    ),
                    const SizedBox(width: 12), // 减小间距

                    // 右侧空状态提示 - 占比约2/3
                    Expanded(
                      child: Container(
                        height: 70, // 固定高度，与圆环保持一致
                        padding: const EdgeInsets.symmetric(vertical: 0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 14,
                                  color: AppColors.textSecondary,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  '今日暂无专注记录',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),

        // 今日项目进度推进卡片
        const SizedBox(height: 16),
        TodayProjectProgressCard(
          projects: _hiveService.subjectRepository.getAllProjects(),
          subjects: _hiveService.subjectRepository.getAllSubjects(),
          todayRecords: todayRecords,
          yesterdayRecords: _getYesterdayRecords(),
          progressChanges: _getProgressChanges(),
          onTap: () {
            // 切换到项目标签页
            final tabController = DefaultTabController.of(context);
            // 切换到"项目"标签页（假设是第二个标签页）
            tabController.animateTo(1);
          },
        ),
      ],
    );
  }

  // 构建总体概览部分
  Widget _buildTotalOverviewSection() {
    // 计算所需的五项数据
    final totalHours = FocusDataCalculator.calculateTotalHours(widget.records);
    final sessionCount = FocusDataCalculator.calculateSessionCount(widget.records);
    final avgMinutes = FocusDataCalculator.calculateAverageMinutes(widget.records);
    final focusDays = FocusDataCalculator.calculateFocusDays(widget.records);
    final avgDailyHours = FocusDataCalculator.calculateAverageDailyHours(widget.records);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 总体概览标题
        const Text(
          '总体概览',
          style: AppTextStyles.headline3,
        ),
        const SizedBox(height: 12),

        // 第一个卡片：总专注时长、专注次数、专注天数（3个指标）
        Container(
          padding: const EdgeInsets.all(AppSizes.paddingMedium),
          decoration: AppDecorations.standardCard(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 卡片标题


              // 主要指标行
              Row(
                children: [
                  // 总专注时长
                  Expanded(
                    child: _buildMetricItem(
                      icon: Icons.access_time,
                      value: '${totalHours.toStringAsFixed(1)}h',
                      label: '总专注时长',
                      color: AppColors.info,
                    ),
                  ),

                  // 专注次数
                  Expanded(
                    child: _buildMetricItem(
                      icon: Icons.repeat,
                      value: '$sessionCount次',
                      label: '专注次数',
                      color: AppColors.success,
                    ),
                  ),

                  // 专注天数
                  Expanded(
                    child: _buildMetricItem(
                      icon: Icons.today,
                      value: '$focusDays天',
                      label: '专注天数',
                      color: AppColors.amber,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // 第二个卡片：平均专注时长、平均每天专注时长（2个指标）- 更小的样式
        Container(
          padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium, vertical: 20),
          decoration: AppDecorations.standardCard(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10), // 非常轻微的阴影
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              // 平均专注时长
              Expanded(
                child: _buildCompactMetricItem(
                  icon: Icons.timer,
                  value: '${avgMinutes.toStringAsFixed(1)}分钟',
                  label: '平均专注时长',
                  color: AppColors.purple,
                ),
              ),

              // 平均每天专注时长
              Expanded(
                child: _buildCompactMetricItem(
                  icon: Icons.today,
                  value: '${avgDailyHours.toStringAsFixed(1)}h/天',
                  label: '平均每天专注',
                  color: AppColors.amber,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 调用父类的build方法
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 页面顶部间距
          const SizedBox(height: 8),

          // 总体概览部分（移到上方）
          _buildTotalOverviewSection(),
          const SizedBox(height: 32),

          // 今日概览部分
          _buildTodayOverviewSection(),
        ],
      ),
    );
  }
}
