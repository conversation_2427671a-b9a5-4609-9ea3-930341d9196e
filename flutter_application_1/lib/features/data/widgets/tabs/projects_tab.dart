import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/models/subject_project.dart';
import '../../../../core/services/hive_service.dart';
import '../../../../core/providers/subscription_provider.dart';
import '../../../../shared/theme/constants.dart';
import '../../../subscription/widgets/premium_lock_content.dart';
import '../charts/subject_donut_chart.dart';
import '../charts/project_ranking_chart.dart';
import '../charts/project_progress_speed_chart.dart';
import '../time_period_selector.dart';
import '../tab_style_period_selector.dart';
import '../project_date_selector.dart';
import '../bottom_sheets/subject_projects_sheet.dart';

/// 项目标签页
/// 显示科目与项目的专注对比情况
class ProjectsTab extends ConsumerStatefulWidget {
  final List<FocusRecord> records;
  final String? subjectId;
  final String? projectId;
  final Function(FocusRecord) onRecordTap;
  final HiveService hiveService;

  const ProjectsTab({
    super.key,
    required this.records,
    this.subjectId,
    this.projectId,
    required this.onRecordTap,
    required this.hiveService,
  });

  @override
  ConsumerState<ProjectsTab> createState() => _ProjectsTabState();
}

class _ProjectsTabState extends ConsumerState<ProjectsTab> with AutomaticKeepAliveClientMixin {
  // 当前选中的时间段
  TimePeriodType _selectedPeriod = TimePeriodType.day;

  // 当前选中的日期
  late DateTime _selectedDate;

  // 根据时间段筛选的记录
  List<FocusRecord> _filteredRecords = [];

  // 所有科目
  List<Subject> _subjects = [];

  // 所有项目
  List<Project> _projects = [];

  // 科目专注时长数据
  Map<String, SubjectFocusData> _subjectFocusData = {};

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // 初始化选中日期为今天
    final now = DateTime.now();
    _selectedDate = DateTime(now.year, now.month, now.day);
    _loadData();
  }

  @override
  void didUpdateWidget(ProjectsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.records != oldWidget.records) {
      debugPrint('ProjectsTab: 检测到records变化，从 ${oldWidget.records.length} 条变为 ${widget.records.length} 条');
      _loadData();
    }
  }

  // 加载数据
  void _loadData() {
    // 获取所有科目
    _subjects = widget.hiveService.subjectRepository.getAllSubjects();

    // 获取所有项目
    _projects = widget.hiveService.subjectRepository.getAllProjects();

    // 根据时间段筛选记录
    _filterRecordsByPeriod();

    // 计算科目专注数据
    _calculateSubjectFocusData();
  }

  // 根据时间段筛选记录
  void _filterRecordsByPeriod() {
    switch (_selectedPeriod) {
      case TimePeriodType.day:
        // 仅包含选中日期的记录
        final selectedDay = DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);
        _filteredRecords = widget.records.where((record) {
          final recordDate = DateTime(
            record.startTime.year,
            record.startTime.month,
            record.startTime.day,
          );
          return recordDate.isAtSameMomentAs(selectedDay);
        }).toList();
        break;

      case TimePeriodType.week:
        // 包含选中周的记录（周一到周日）
        final selectedDay = DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);
        final weekStart = selectedDay.subtract(Duration(days: selectedDay.weekday - 1));
        final weekEnd = weekStart.add(const Duration(days: 7));

        _filteredRecords = widget.records.where((record) {
          final recordDate = DateTime(
            record.startTime.year,
            record.startTime.month,
            record.startTime.day,
          );
          return recordDate.isAfter(weekStart.subtract(const Duration(days: 1))) &&
                 recordDate.isBefore(weekEnd);
        }).toList();
        break;

      case TimePeriodType.month:
        // 包含选中月的记录
        _filteredRecords = widget.records.where((record) {
          return record.startTime.year == _selectedDate.year &&
                 record.startTime.month == _selectedDate.month;
        }).toList();
        break;
    }
  }

  // 计算科目专注数据
  void _calculateSubjectFocusData() {
    _subjectFocusData = {};

    // 初始化所有科目的数据
    for (final subject in _subjects) {
      _subjectFocusData[subject.id] = SubjectFocusData(
        subject: subject,
        focusHours: 0,
        sessionCount: 0,
        avgSessionMinutes: 0,
      );
    }

    // 统计每个科目的专注数据
    for (final record in _filteredRecords) {
      if (!_subjectFocusData.containsKey(record.subjectId)) continue;

      final data = _subjectFocusData[record.subjectId]!;
      final hours = record.durationSeconds / 3600.0;

      _subjectFocusData[record.subjectId] = SubjectFocusData(
        subject: data.subject,
        focusHours: data.focusHours + hours,
        sessionCount: data.sessionCount + 1,
        avgSessionMinutes: data.sessionCount > 0
            ? ((data.focusHours * 60 + hours * 60) / (data.sessionCount + 1))
            : hours * 60,
      );
    }
  }

  // 切换时间段
  void _onPeriodChanged(TimePeriodType period) {
    setState(() {
      _selectedPeriod = period;
      _filterRecordsByPeriod();
      _calculateSubjectFocusData();
    });
  }

  // 切换到前一天/周/月
  void _onPrevious() {
    setState(() {
      switch (_selectedPeriod) {
        case TimePeriodType.day:
          _selectedDate = _selectedDate.subtract(const Duration(days: 1));
          break;
        case TimePeriodType.week:
          _selectedDate = _selectedDate.subtract(const Duration(days: 7));
          break;
        case TimePeriodType.month:
          // 前一个月
          if (_selectedDate.month == 1) {
            _selectedDate = DateTime(_selectedDate.year - 1, 12, _selectedDate.day);
          } else {
            _selectedDate = DateTime(_selectedDate.year, _selectedDate.month - 1, _selectedDate.day);
          }
          break;
      }
      _filterRecordsByPeriod();
      _calculateSubjectFocusData();
    });
  }

  // 切换到后一天/周/月
  void _onNext() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // 不允许选择超过今天的日期
    DateTime nextDate;
    switch (_selectedPeriod) {
      case TimePeriodType.day:
        nextDate = _selectedDate.add(const Duration(days: 1));
        break;
      case TimePeriodType.week:
        nextDate = _selectedDate.add(const Duration(days: 7));
        break;
      case TimePeriodType.month:
        // 后一个月
        if (_selectedDate.month == 12) {
          nextDate = DateTime(_selectedDate.year + 1, 1, _selectedDate.day);
        } else {
          nextDate = DateTime(_selectedDate.year, _selectedDate.month + 1, _selectedDate.day);
        }
        break;
    }

    // 如果下一个日期不超过今天，则切换
    if (!nextDate.isAfter(today)) {
      setState(() {
        _selectedDate = nextDate;
        _filterRecordsByPeriod();
        _calculateSubjectFocusData();
      });
    }
  }

  // 返回今天
  void _onToday() {
    setState(() {
      final now = DateTime.now();
      _selectedDate = DateTime(now.year, now.month, now.day);
      _filterRecordsByPeriod();
      _calculateSubjectFocusData();
    });
  }

  // 显示科目项目详情底部弹窗
  void _showSubjectProjectsSheet(BuildContext context, Subject subject) {
    // 获取该科目的所有项目
    final subjectProjects = _projects.where((p) => p.subjectId == subject.id).toList();

    // 获取该科目的专注数据
    final subjectData = _subjectFocusData[subject.id];

    // 获取时间段标题
    String periodTitle;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final isToday = _selectedDate.year == today.year &&
                    _selectedDate.month == today.month &&
                    _selectedDate.day == today.day;

    switch (_selectedPeriod) {
      case TimePeriodType.day:
        if (isToday) {
          periodTitle = '今日';
        } else {
          periodTitle = '${_selectedDate.month}月${_selectedDate.day}日';
        }
        break;

      case TimePeriodType.week:
        // 获取所选周的周一
        final weekStart = _selectedDate.subtract(Duration(days: _selectedDate.weekday - 1));
        // 获取所选周的周日
        final weekEnd = weekStart.add(const Duration(days: 6));

        if (weekStart.month == weekEnd.month) {
          // 同一个月内
          periodTitle = '${weekStart.month}月${weekStart.day}-${weekEnd.day}日';
        } else {
          // 跨月
          periodTitle = '${weekStart.month}月${weekStart.day}日-${weekEnd.month}月${weekEnd.day}日';
        }
        break;

      case TimePeriodType.month:
        if (_selectedDate.year == today.year && _selectedDate.month == today.month) {
          periodTitle = '本月';
        } else {
          periodTitle = '${_selectedDate.year}年${_selectedDate.month}月';
        }
        break;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SubjectProjectsSheet(
        subject: subject,
        projects: subjectProjects,
        records: _filteredRecords,
        periodType: _selectedPeriod,
        periodTitle: periodTitle,
        focusHours: subjectData?.focusHours ?? 0,
        sessionCount: subjectData?.sessionCount ?? 0,
        avgSessionMinutes: subjectData?.avgSessionMinutes ?? 0,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 调用父类的build方法

    // 检查订阅状态
    final subscriptionStatus = ref.watch(appleSubscriptionStatusProvider);

    return subscriptionStatus.when(
      data: (isPremium) {
        // 如果不是付费用户，显示锁定页面
        if (!isPremium) {
          return const PremiumLockContent(featureType: 'project');
        }

        // 付费用户显示正常内容
        return _buildContent();
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) {
        // 出错时默认显示锁定页面
        return const PremiumLockContent(featureType: 'project');
      },
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部垂直间距
          const SizedBox(height: AppSizes.paddingMedium),

          // 时间段选择器和日期选择器容器
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
            child: SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // 时间段选择器（放在顶部）- 使用新的Tab样式
                  TabStylePeriodSelector<TimePeriodType>(
                    selectedPeriod: _selectedPeriod,
                    onPeriodChanged: _onPeriodChanged,
                    showYearOption: false,
                    useTimePeriodType: true,
                  ),

                  const SizedBox(height: 12),

                  // 日期选择器
                  ProjectDateSelector(
                    selectedDate: _selectedDate,
                    periodType: _selectedPeriod,
                    onPrevious: _onPrevious,
                    onNext: _onNext,
                    onToday: _onToday,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 科目专注分布标题（放在选择器下方，卡片上方的左边）
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
            child: const Text(
              '科目专注分布',
              style: AppTextStyles.headline3,
            ),
          ),

          const SizedBox(height: 12),

          // 科目专注分布卡片
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingMedium),
                child: _filteredRecords.isEmpty
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(32),
                        child: Text('暂无专注数据'),
                      ),
                    )
                  : SubjectDonutChart(
                      records: _filteredRecords,
                      subjects: _subjects,
                      onSubjectTap: (subject) => _showSubjectProjectsSheet(context, subject),
                    ),
              ),
            ),
          ),

          const SizedBox(height: AppSizes.paddingMedium),

          // 项目专注时间排行卡片
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingMedium),
                child: _filteredRecords.isEmpty
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(32),
                        child: Text('暂无专注数据'),
                      ),
                    )
                  : SizedBox(
                      height: 350, // 设置固定高度
                      child: ProjectRankingChart(
                        records: _filteredRecords,
                        projects: _projects,
                        subjects: _subjects,
                      ),
                    ),
              ),
            ),
          ),

          const SizedBox(height: AppSizes.paddingMedium),

          // 项目专注推进速度卡片
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingMedium),
                child: _filteredRecords.isEmpty
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(32),
                        child: Text('暂无专注数据'),
                      ),
                    )
                  : SizedBox(
                      height: 350, // 设置固定高度
                      child: ProjectProgressSpeedChart(
                        records: _filteredRecords,
                        projects: _projects,
                        subjects: _subjects,
                      ),
                    ),
              ),
            ),
          ),

          // 底部垂直间距
          const SizedBox(height: AppSizes.paddingMedium),
        ],
      ),
    );
  }
}

/// 科目专注数据
class SubjectFocusData {
  final Subject subject;
  final double focusHours;
  final int sessionCount;
  final double avgSessionMinutes;

  SubjectFocusData({
    required this.subject,
    required this.focusHours,
    required this.sessionCount,
    required this.avgSessionMinutes,
  });
}


