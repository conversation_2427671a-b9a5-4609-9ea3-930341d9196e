import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/theme/constants.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/utils/date_util.dart';
import '../../../../core/providers/subscription_provider.dart';
import '../../utils/time_period_utils.dart';
import '../../utils/focus_data_calculator.dart';
import '../../../subscription/widgets/premium_lock_content.dart';
import '../tab_style_period_selector.dart';
import '../date_selector.dart';
import '../cards/focus_statistics_card.dart';

import '../charts/daily_hourly_line_chart.dart';
import '../charts/weekly_bar_chart.dart';
import '../charts/monthly_bar_chart.dart';
import '../charts/yearly_bar_chart.dart';
import '../charts/monthly_heatmap.dart';
import '../charts/weekly_heatmap.dart';
import '../cards/best_focus_period_card_new.dart';
import '../cards/monthly_comparison_card_new.dart';
import '../cards/weekly_comparison_card.dart';

/// 专注趋势标签页
/// 显示不同时间周期的专注趋势
class TrendTab extends ConsumerStatefulWidget {
  final List<FocusRecord> allRecords;
  final Function(List<FocusRecord>) onRecordsFiltered;

  const TrendTab({
    super.key,
    required this.allRecords,
    required this.onRecordsFiltered,
  });

  @override
  ConsumerState<TrendTab> createState() => _TrendTabState();
}

class _TrendTabState extends ConsumerState<TrendTab> with AutomaticKeepAliveClientMixin {
  // 当前选择的时间周期
  TimePeriod _selectedPeriod = TimePeriod.week;

  // 当前选择的日期
  DateTime _selectedDate = DateTime.now();

  // 过滤后的记录
  List<FocusRecord> _filteredRecords = [];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // 初始化时设置为当前日期
    _selectedDate = DateTime.now();

    // 使用延迟回调，确保在构建完成后再更新记录
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateFilteredRecords();
      }
    });
  }

  @override
  void didUpdateWidget(TrendTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当传入的allRecords发生变化时，重新过滤记录
    if (widget.allRecords != oldWidget.allRecords) {
      debugPrint('TrendTab: 检测到allRecords变化，从 ${oldWidget.allRecords.length} 条变为 ${widget.allRecords.length} 条');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _updateFilteredRecords();
        }
      });
    }
  }

  // 更新过滤的记录
  void _updateFilteredRecords() {
    List<FocusRecord> filtered = [];

    switch (_selectedPeriod) {
      case TimePeriod.day:
        final dayStart = DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);
        final dayEnd = dayStart.add(const Duration(days: 1)).subtract(const Duration(seconds: 1));

        filtered = widget.allRecords.where((record) {
          return record.startTime.isAfter(dayStart) && record.startTime.isBefore(dayEnd);
        }).toList();
        break;

      case TimePeriod.week:
        final weekStart = TimePeriodUtils.getWeekStart(_selectedDate);
        final weekEnd = weekStart.add(const Duration(days: 7)).subtract(const Duration(seconds: 1));

        filtered = widget.allRecords.where((record) {
          return record.startTime.isAfter(weekStart) && record.startTime.isBefore(weekEnd);
        }).toList();
        break;

      case TimePeriod.month:
        final monthStart = DateTime(_selectedDate.year, _selectedDate.month, 1);
        final monthEnd = _selectedDate.month < 12
            ? DateTime(_selectedDate.year, _selectedDate.month + 1, 1).subtract(const Duration(seconds: 1))
            : DateTime(_selectedDate.year + 1, 1, 1).subtract(const Duration(seconds: 1));

        filtered = widget.allRecords.where((record) {
          return record.startTime.isAfter(monthStart) && record.startTime.isBefore(monthEnd);
        }).toList();
        break;

      case TimePeriod.year:
        final yearStart = DateTime(_selectedDate.year, 1, 1);
        final yearEnd = DateTime(_selectedDate.year + 1, 1, 1).subtract(const Duration(seconds: 1));

        filtered = widget.allRecords.where((record) {
          return record.startTime.isAfter(yearStart) && record.startTime.isBefore(yearEnd);
        }).toList();
        break;
    }

    // 按时间排序（最新的在前）
    filtered.sort((a, b) => b.startTime.compareTo(a.startTime));

    setState(() {
      _filteredRecords = filtered;
    });

    // 使用延迟回调，确保在构建完成后再通知父组件
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        widget.onRecordsFiltered(filtered);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 调用父类的build方法

    // 检查订阅状态
    final subscriptionStatus = ref.watch(appleSubscriptionStatusProvider);

    return subscriptionStatus.when(
      data: (isPremium) {
        // 如果不是付费用户，显示锁定页面
        if (!isPremium) {
          return const PremiumLockContent(featureType: 'focus');
        }

        // 付费用户显示正常内容
        return _buildContent();
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) {
        // 出错时默认显示锁定页面
        return const PremiumLockContent(featureType: 'focus');
      },
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间周期选择器 - 使用新的Tab样式
          TabStylePeriodSelector<TimePeriod>(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
                _updateFilteredRecords();
              });
            },
            showYearOption: true,
            useTimePeriodType: false,
          ),
          const SizedBox(height: 16),

          // 时间选择器
          DateSelector(
            selectedDate: _selectedDate,
            selectedPeriod: _selectedPeriod,
            onDateChanged: (date) {
              setState(() {
                _selectedDate = date;
                _updateFilteredRecords();
              });
            },
          ),
          const SizedBox(height: 20),

          // 专注统计数据标题
          Text(
            TimePeriodUtils.getPeriodTitle(_selectedPeriod),
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 12),

          // 专注统计数据卡片
          FocusStatisticsCard(
            selectedPeriod: _selectedPeriod,
            totalHours: FocusDataCalculator.calculateTotalHours(_filteredRecords),
            sessionCount: FocusDataCalculator.calculateSessionCount(_filteredRecords),
            avgMinutes: FocusDataCalculator.calculateAverageMinutes(_filteredRecords),
            records: _filteredRecords, // 传递记录列表，用于计算打断次数和平均日专注
          ),
          const SizedBox(height: 20),

          // 专注时长趋势图 - 周视图使用新组件
          if (_selectedPeriod == TimePeriod.week) ...[
            Text(
              '专注时长趋势',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 12),
            Container(
              height: 250,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [AppShadows.low],
              ),
              child: _filteredRecords.isEmpty
                  ? const Center(child: Text('暂无数据'))
                  : WeeklyBarChart(
                      records: _filteredRecords,
                      selectedDate: _selectedDate,
                    ),
            ),
            const SizedBox(height: 20),

            // 周专注对比卡片
            Text(
              '周专注对比',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(15),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: WeeklyComparisonCard(
                records: widget.allRecords, // 使用所有记录，以便计算上周数据
                selectedDate: _selectedDate,
              ),
            ),
            const SizedBox(height: 20),

            // 周专注热力图
            Text(
              '本周专注热力图',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [AppShadows.low],
              ),
              child: _filteredRecords.isEmpty
                  ? const Center(child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text('暂无数据'),
                    ))
                  : WeeklyHeatmap(
                      records: _filteredRecords,
                      selectedDate: _selectedDate,
                    ),
            ),
            const SizedBox(height: 24),
          ],

          // 专注时长趋势图 - 月视图
          if (_selectedPeriod == TimePeriod.month) ...[
            Text(
              '专注时长趋势',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 12),
            Container(
              height: 250,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [AppShadows.low],
              ),
              child: _filteredRecords.isEmpty
                  ? const Center(child: Text('暂无数据'))
                  : MonthlyBarChart(
                      records: _filteredRecords,
                      selectedDate: _selectedDate,
                    ),
            ),
            const SizedBox(height: 20),

            // 月度专注对比卡片
            Text(
              '月专注对比',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(15),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: MonthlyComparisonCardNew(
                records: widget.allRecords, // 使用所有记录，以便计算上月数据
                selectedDate: _selectedDate,
              ),
            ),
            const SizedBox(height: 20),

            // 月视图专注热力图
            Text(
              '本月专注热力图',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [AppShadows.low],
              ),
              child: _filteredRecords.isEmpty
                  ? const Center(child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text('暂无数据'),
                    ))
                  : MonthlyHeatmap(
                      records: _filteredRecords,
                      selectedDate: _selectedDate,
                    ),
            ),
            const SizedBox(height: 24),
          ],

          // 专注时长趋势图 - 年视图
          if (_selectedPeriod == TimePeriod.year) ...[
            Text(
              '专注时长趋势',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 12),
            Container(
              height: 250,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [AppShadows.low],
              ),
              child: _filteredRecords.isEmpty
                  ? const Center(child: Text('暂无数据'))
                  : YearlyBarChart(
                      records: _filteredRecords,
                      selectedDate: _selectedDate,
                    ),
            ),
            const SizedBox(height: 24),
          ],

          // 日视图下的小时分布图
          if (_selectedPeriod == TimePeriod.day) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '当日专注时段分布',
                  style: AppTextStyles.headline3,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  // child: Row(
                  //   mainAxisSize: MainAxisSize.min,
                  //   children: [
                  //     Icon(Icons.info_outline, size: 14, color: Colors.grey.shade600),
                  //     const SizedBox(width: 4),
                  //     Text('按小时分布', style: TextStyle(fontSize: 12, color: Colors.grey.shade600)),
                  //   ],
                  // ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              height: 280, // 增加高度，以容纳标签
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [AppShadows.low],
              ),
              child: DailyHourlyLineChart(records: _filteredRecords),
            ),
            const SizedBox(height: 24),
          ],

          // 最佳专注时段分析 - 在周视图和月视图下显示（仅显示已完成的周期）
          if (_selectedPeriod == TimePeriod.week && _isPastWeek(_selectedDate)) ...[
            // 周视图下显示连续三小时的最佳专注时段
            Text(
              '最佳专注时段分析',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 12),
            BestFocusPeriodCardNew(
              records: _filteredRecords,
              selectedDate: _selectedDate,
              isWeekView: true,
            ),
          ],

          // 月视图下也显示最佳专注时段分析（仅显示已完成的周期）
          if (_selectedPeriod == TimePeriod.month && _isPastMonth(_selectedDate)) ...[
            // 月视图下显示连续三小时的最佳专注时段
            Text(
              '最佳专注时段分析',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 12),
            BestFocusPeriodCardNew(
              records: _filteredRecords,
              selectedDate: _selectedDate,
              isWeekView: false,
            ),
          ],
        ],
      ),
    );
  }

  /// 判断所选周是否为过去的周（已完成）
  bool _isPastWeek(DateTime date) {
    final weekRange = DateUtil.getWeekRange(date);
    final now = DateTime.now();
    return now.isAfter(weekRange.end);
  }

  /// 判断所选月是否为过去的月（已完成）
  bool _isPastMonth(DateTime date) {
    final monthEnd = DateTime(date.year, date.month + 1, 0);
    final now = DateTime.now();
    return now.isAfter(monthEnd);
  }
}
