import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/subscription_provider.dart';
import '../../../shared/theme/constants.dart';
import '../../../utils/url_launcher_helper.dart';
import '../widgets/subscription_status_card.dart';

/// 高级订阅页面
/// 重新设计的订阅界面，包含新的产品和更精美的设计
class PremiumSubscriptionScreen extends ConsumerStatefulWidget {
  const PremiumSubscriptionScreen({super.key});

  @override
  ConsumerState<PremiumSubscriptionScreen> createState() => _PremiumSubscriptionScreenState();
}

class _PremiumSubscriptionScreenState extends ConsumerState<PremiumSubscriptionScreen> {
  bool _isLoading = false;
  String? _selectedProductId;
  bool _isDisposed = false;

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final subscriptionProducts = ref.watch(appleSubscriptionProductsProvider);
    final subscriptionStatus = ref.watch(appleSubscriptionStatusProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.black87),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'LimeFocus Pro',
          style: TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: subscriptionProducts.when(
        data: (products) => _buildContent(products, subscriptionStatus),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildContent(List<Map<String, dynamic>> products, AsyncValue<bool> statusAsync) {
    return Column(
      children: [
        // 订阅状态卡片（仅对已订阅用户显示）
        const SubscriptionStatusCard(),

        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 32),
                _buildBasicFeatures(),
                const SizedBox(height: 32),
                _buildFeaturesList(),
                const SizedBox(height: 32),
                _buildProductCards(products),
              ],
            ),
          ),
        ),
        _buildBottomSection(products),
      ],
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Text(
            '🚀 解锁全部功能',
            style: TextStyle(
              color: AppColors.primary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          '专注备考，\n高效复盘',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
            height: 1.2,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '升级到 Pro 版本，解锁更多功能，让备考更高效',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
            height: 1.4,
          ),
        ),
      ],
    );
  }

  Widget _buildBasicFeatures() {
    final basicFeatures = [
      {'icon': '🎯', 'title': '日程与专注功能', 'desc': '基础的专注与日程体系，专注活动与项目进度绑定'},
      {'icon': '📊', 'title': '基础数据分析', 'desc': '基本的每日专注数据情况与项目专注记录'},
      {'icon': '🎓', 'title': '备考目标体系', 'desc': '聚焦核心目标的机制，围绕上岸制定备考计划'},
      {'icon': '📋', 'title': '科目与项目制', 'desc': '追踪科目与项目的进度与效率，基础版可最多创建8个进度追踪项目'},
      {'icon': '🔄', 'title': '持续更新完善', 'desc': '后续不断更新完善的基础功能'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '基础功能',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '满足基本的学习需求',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 16),
        ...basicFeatures.map((feature) => _buildFeatureItem(
          feature['icon']!,
          feature['title']!,
          feature['desc']!,
        )),
      ],
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      {'icon': '📊', 'title': '深度数据分析', 'desc': '专注趋势、效率评估、个性化建议'},
      {'icon': '📈', 'title': '项目进度追踪', 'desc': '无限项目管理、进度可视化、完成预测'},
      // {'icon': '☁️', 'title': '云端同步备份', 'desc': '多设备同步、数据安全保障'},
      // {'icon': '🎯', 'title': '高级专注模式', 'desc': '番茄钟、白噪音、专注环境定制'},
      // {'icon': '🔔', 'title': '智能提醒系统', 'desc': '学习计划提醒、休息建议、目标跟踪'},
      // {'icon': '🎨', 'title': '个性化定制', 'desc': '主题切换、界面定制、专属体验'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '高级功能',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        ...features.map((feature) => _buildFeatureItem(
          feature['icon']!,
          feature['title']!,
          feature['desc']!,
        )),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Row(
            children: [
              const Icon(Icons.info_outline, color: Colors.blue, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  '更多高级功能正在开发中，后续将同步更新自动解锁',
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureItem(String icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Center(
              child: Text(
                icon,
                style: const TextStyle(fontSize: 20),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCards(List<Map<String, dynamic>> products) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '选择订阅计划',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        ...products.map((product) => _buildProductCard(product)),
      ],
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    final isSelected = _selectedProductId == product['id'];
    final isRecommended = product['id'] == 'LimeVip_AYear'; // 推荐一年备考包

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedProductId = product['id'];
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withValues(alpha: 0.05) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            product['title'] ?? '',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          if (isRecommended) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                '推荐',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        product['description'] ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      product['price'] ?? '',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    if (product['id'] == 'LimeVip_AYear')
                      Text(
                        '限时优惠',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSection(List<Map<String, dynamic>> products) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _selectedProductId != null && !_isLoading
                  ? () => _purchaseProduct(_selectedProductId!)
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      _selectedProductId != null ? '立即订阅' : '请选择订阅计划',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                onPressed: () => _restorePurchases(),
                child: const Text(
                  '恢复购买',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ),
              const Text(' • ', style: TextStyle(color: Colors.grey)),
              TextButton(
                onPressed: () {
                  UrlLauncherHelper.launchTermsWithErrorHandling(context);
                },
                child: const Text(
                  '服务条款',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ),
              const Text(' • ', style: TextStyle(color: Colors.grey)),
              TextButton(
                onPressed: () {
                  UrlLauncherHelper.launchPrivacyWithErrorHandling(context);
                },
                child: const Text(
                  '隐私政策',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(appleSubscriptionProductsProvider);
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 刷新订阅状态
  Future<void> _refreshSubscriptionStatus() async {
    if (_isDisposed) {
      debugPrint('页面已销毁，跳过订阅状态刷新');
      return;
    }

    try {
      final service = ref.read(appleSubscriptionServiceProvider);
      await service.refreshSubscriptionStatus();

      // 检查页面是否仍然存在
      if (!_isDisposed && mounted) {
        // 刷新Riverpod状态
        ref.invalidate(appleSubscriptionStatusProvider);
        debugPrint('订阅状态刷新完成');
      } else {
        debugPrint('页面已销毁，跳过状态更新');
      }
    } catch (e) {
      debugPrint('刷新订阅状态失败: $e');
    }
  }

  Future<void> _purchaseProduct(String productId) async {
    if (_isDisposed) {
      debugPrint('页面已销毁，跳过购买操作');
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      final service = ref.read(appleSubscriptionServiceProvider);
      final success = await service.purchaseSubscription(
        productId,
        callback: (success, error) {
          // 双重检查：页面未销毁且仍然mounted
          if (!_isDisposed && mounted) {
            setState(() {
              _isLoading = false;
            });

            if (success) {
              _showSuccess('订阅成功！感谢您的支持！');
              // 强制刷新订阅状态
              _refreshSubscriptionStatus();
              Navigator.of(context).pop();
            } else {
              // 优化错误处理，只显示用户需要知道的错误
              _handlePurchaseError(error);
            }
          } else {
            debugPrint('页面已销毁，跳过购买回调处理');
          }
        },
      );

      if (!success && !_isDisposed && mounted) {
        setState(() {
          _isLoading = false;
        });
        // 不显示技术性错误信息
        debugPrint('购买请求失败');
      }
    } catch (e) {
      if (!_isDisposed && mounted) {
        setState(() {
          _isLoading = false;
        });
        // 只在开发模式下显示详细错误
        debugPrint('购买过程中发生错误: $e');
        _handlePurchaseError(e.toString());
      } else {
        debugPrint('页面已销毁，跳过错误处理');
      }
    }
  }

  void _handlePurchaseError(String? error) {
    if (error == null) return;

    debugPrint('处理购买错误: $error');

    // 过滤掉不需要向用户显示的错误
    if (error.contains('storekit2_purchase_cancelled') ||
        error.contains('cancelled by the user') ||
        error.contains('User cancelled')) {
      // 用户取消购买，不显示错误信息
      debugPrint('用户取消了购买');
      return;
    }

    if (error.contains('No active account') ||
        error.contains('ASDErrorDomain Code=509')) {
      // 模拟器或测试环境的错误，不显示给用户
      debugPrint('模拟器环境错误: $error');
      _showError('请在真机上测试购买功能');
      return;
    }

    // 处理沙盒账号相关错误
    if (error.contains('Invalid credentials') ||
        error.contains('账号或密码错误') ||
        error.contains('authentication') ||
        error.contains('sign in') ||
        error.contains('login')) {
      debugPrint('沙盒账号登录错误: $error');
      _showSandboxAccountError();
      return;
    }

    // 处理产品配置相关错误
    if (error.contains('产品不存在') ||
        error.contains('Product not found') ||
        error.contains('Invalid product')) {
      debugPrint('产品配置错误: $error');
      _showError('产品配置错误，请稍后重试');
      return;
    }

    // 显示用户友好的错误信息
    if (error.contains('network') || error.contains('连接')) {
      _showError('网络连接失败，请检查网络后重试');
    } else if (error.contains('payment') || error.contains('支付')) {
      _showError('支付失败，请稍后重试');
    } else {
      _showError('购买失败，请稍后重试');
    }
  }

  void _showSandboxAccountError() {
    if (!_isDisposed && mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('沙盒测试账号问题'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('请检查以下事项：'),
              SizedBox(height: 8),
              Text('1. 确保使用的是沙盒测试账号'),
              Text('2. 账号密码输入正确'),
              Text('3. 在设备设置中退出真实Apple ID'),
              Text('4. 确保在真机上测试'),
              SizedBox(height: 8),
              Text('沙盒测试账号需要在App Store Connect中创建。'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('知道了'),
            ),
          ],
        ),
      );
    }
  }

  Future<void> _restorePurchases() async {
    if (_isDisposed) {
      debugPrint('页面已销毁，跳过恢复购买操作');
      return;
    }

    try {
      final service = ref.read(appleSubscriptionServiceProvider);

      // 显示加载提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('正在恢复购买...'),
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      final success = await service.restorePurchases();

      if (!_isDisposed && mounted) {
        if (success) {
          _showSuccess('购买恢复成功');
          ref.invalidate(appleSubscriptionStatusProvider);
        } else {
          // 检查是否是模拟器环境
          _handleRestoreError('未找到可恢复的购买记录');
        }
      } else {
        debugPrint('页面已销毁，跳过恢复购买结果处理');
      }
    } catch (e) {
      debugPrint('恢复购买失败: $e');
      if (!_isDisposed && mounted) {
        _handleRestoreError(e.toString());
      }
    }
  }

  void _handleRestoreError(String error) {
    // 过滤模拟器相关错误
    if (error.contains('No active account') ||
        error.contains('ASDErrorDomain Code=509')) {
      debugPrint('模拟器环境错误: $error');
      _showError('请在真机上测试恢复购买功能');
      return;
    }

    // 显示用户友好的错误信息
    if (error.contains('未找到') || error.contains('no purchases')) {
      _showError('未找到可恢复的购买记录');
    } else if (error.contains('network') || error.contains('连接')) {
      _showError('网络连接失败，请检查网络后重试');
    } else {
      _showError('恢复购买失败，请稍后重试');
    }
  }

  void _showSuccess(String message) {
    if (!_isDisposed && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _showError(String message) {
    if (!_isDisposed && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}
