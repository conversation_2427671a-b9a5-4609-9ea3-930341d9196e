import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../core/providers/subscription_provider.dart';
import '../../../shared/theme/constants.dart';

/// 订阅状态显示卡片
/// 在解锁Pro界面顶部显示用户当前的订阅情况
class SubscriptionStatusCard extends ConsumerWidget {
  const SubscriptionStatusCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subscriptionStatus = ref.watch(appleSubscriptionStatusProvider);
    
    return subscriptionStatus.when(
      data: (isPremium) {
        if (isPremium) {
          return _buildPremiumStatusCard(context, ref);
        } else {
          return const SizedBox.shrink(); // 未订阅时不显示
        }
      },
      loading: () => _buildLoadingCard(),
      error: (error, _) => const SizedBox.shrink(),
    );
  }

  /// 构建高级用户状态卡片
  Widget _buildPremiumStatusCard(BuildContext context, WidgetRef ref) {
    return FutureBuilder<Map<String, dynamic>?>(
      future: ref.read(appleSubscriptionServiceProvider).getSubscriptionDetails(),
      builder: (context, snapshot) {
        final subscriptionDetails = snapshot.data;
        
        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary.withValues(alpha: 0.1),
                AppColors.primary.withValues(alpha: 0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部状态行
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.verified,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        const Text(
                          'Pro 用户',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.star,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // 订阅信息
              if (subscriptionDetails != null) ...[
                _buildSubscriptionInfo(subscriptionDetails),
              ] else ...[
                _buildDefaultInfo(),
              ],
            ],
          ),
        );
      },
    );
  }

  /// 构建订阅详细信息
  Widget _buildSubscriptionInfo(Map<String, dynamic> details) {
    final productId = details['productId'] as String?;
    final purchaseDate = details['purchaseDate'] as String?;
    final expiryDate = details['expiryDate'] as String?;
    final daysRemaining = details['daysRemaining'] as int? ?? 0;
    
    // 解析订阅类型
    final subscriptionType = _getSubscriptionTypeName(productId);
    
    // 解析日期
    DateTime? purchaseDateParsed;
    DateTime? expiryDateParsed;
    
    if (purchaseDate != null) {
      purchaseDateParsed = DateTime.tryParse(purchaseDate);
    }
    
    if (expiryDate != null) {
      expiryDateParsed = DateTime.tryParse(expiryDate);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 订阅类型
        Text(
          subscriptionType,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // 订阅信息行
        Row(
          children: [
            Expanded(
              child: _buildInfoItem(
                icon: Icons.calendar_today,
                label: '购买日期',
                value: purchaseDateParsed != null 
                    ? DateFormat('yyyy年MM月dd日').format(purchaseDateParsed)
                    : '未知',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildInfoItem(
                icon: Icons.schedule,
                label: '到期时间',
                value: expiryDateParsed != null 
                    ? DateFormat('yyyy年MM月dd日').format(expiryDateParsed)
                    : '未知',
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // 剩余天数
        if (daysRemaining > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: daysRemaining > 7 
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: daysRemaining > 7 
                    ? Colors.green.withValues(alpha: 0.3)
                    : Colors.orange.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  daysRemaining > 7 ? Icons.check_circle : Icons.warning,
                  color: daysRemaining > 7 ? Colors.green : Colors.orange,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  '剩余 $daysRemaining 天',
                  style: TextStyle(
                    color: daysRemaining > 7 ? Colors.green.shade700 : Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 构建默认信息（当无法获取详细信息时）
  Widget _buildDefaultInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'LimeFocus Pro',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          '感谢您的支持！您已解锁所有高级功能',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 构建信息项
  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 14,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  /// 构建加载状态卡片
  Widget _buildLoadingCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 12),
          Text(
            '正在检查订阅状态...',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取订阅类型名称
  String _getSubscriptionTypeName(String? productId) {
    switch (productId) {
      case 'LemiVip001':
        return 'LimeFocus Pro 月度订阅';
      case 'LimeVip_quarter':
        return 'LimeFocus Pro 季度订阅';
      case 'LimeVip_yearly':
        return 'LimeFocus Pro 年度订阅';
      case 'LimeVip_AYear':
        return 'LimeFocus 一年备考包';
      default:
        return 'LimeFocus Pro';
    }
  }
}
